"use client";

import { useState, useMemo, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Bookmark,
  List,
  ImageIcon,
  X,
  ChevronLeft,
  Settings,
  PenTool,
  Download,
  Printer,
  Grid3X3,
  Activity,
  FileText,
  Edit,
  CheckCircle,
  GitBranch,
  Eye,
  Shield,
  Users,
  Accessibility,
  History,
  Clock,
  ChevronDown,
  ChevronRight,
  Edit3,
  Layers,
} from "lucide-react";
import { cn } from "@/lib/utils";
import PDFSearch from "@/components/search/pdf-search";
import PDFBookmarks from "@/components/navigation/pdf-bookmarks";
import PDFOutline from "@/components/navigation/pdf-outline";
import PDFImageExtractor from "@/components/tools/pdf-image-extractor";
import PDFAnnotations, {
  type Annotation,
  type AnnotationType,
} from "@/components/annotations/pdf-annotations";

import PDFAnnotationExport from "@/components/annotations/pdf-annotation-export";
import PDFPrintManager from "@/components/tools/pdf-print-manager";
import PDFThumbnailView from "@/components/navigation/pdf-thumbnail-view";
import PDFPerformanceMonitor from "@/components/tools/pdf-performance-monitor";
import PDFFormManager, {
  type FormField,
  type FormValidationResult,
  type FormData,
} from "@/components/forms/pdf-form-manager";
import PDFFormDesigner from "@/components/forms/pdf-form-designer";
import PDFFormValidation from "@/components/forms/pdf-form-validation";
import PDFWorkflowManager from "@/components/workflow/pdf-workflow-manager";
import PDFOCREngine from "@/components/tools/pdf-ocr-engine";
import PDFDigitalSignature from "@/components/tools/pdf-digital-signature";
import PDFCollaboration from "@/components/collaboration/pdf-collaboration";
import PDFAccessibility from "@/components/accessibility/pdf-accessibility";
import PDFVersionControl from "@/components/workflow/pdf-version-control";
import PDFVersionTimeline from "@/components/workflow/pdf-version-timeline";
import type { OutlineItem } from "@/lib/types/pdf";

export type ViewerMode = 'reading' | 'annotating' | 'form-filling' | 'reviewing';

type SidebarTab =
  "outline"
  | "search"
  | "bookmarks"
  | "images"
  | "annotations"
  | "export"
  | "print"
  | "thumbnails"
  | "performance"
  | "forms"
  | "form-designer"
  | "form-validation"
  | "workflows"
  | "ocr"
  | "signatures"
  | "collaboration"
  | "accessibility"
  | "version-control"
  | "version-timeline"
  | "settings";

interface SidebarTabConfig {
  id: SidebarTab;
  label: string;
  icon: React.ComponentType<{ className?: string }>;
  badge?: string | number | null;
  modes: ViewerMode[];
  category: string;
}

interface PDFSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  activeTab: SidebarTab;
  onTabChange: (tab: SidebarTab) => void;

  // Adaptive features
  currentMode?: ViewerMode;
  onModeChange?: (mode: ViewerMode) => void;
  adaptiveLayout?: boolean;
  performanceMode?: boolean;
  isCollapsible?: boolean;
  isCollapsed?: boolean;
  onCollapsedChange?: (collapsed: boolean) => void;
  width?: number;
  onWidthChange?: (width: number) => void;

  // PDF data
  pdfDocument: unknown;
  numPages: number;
  currentPage: number;
  outline: OutlineItem[];
  currentScale: number;

  // Search
  searchText: string;
  onSearchChange: (text: string) => void;
  onPageSelect: (page: number) => void;
  searchResults?: Array<{ pageIndex: number; textItems: unknown[] }>;
  onSearchResultsChange?: (results: Array<{ pageIndex: number; textItems: unknown[] }>) => void;

  // Bookmarks
  bookmarks: Array<{
    id: string;
    page: number;
    title: string;
    timestamp: number;
  }>;
  onAddBookmark: () => void;
  onRemoveBookmark: (id: string) => void;
  onUpdateBookmark: (id: string, title: string) => void;

  // Annotations
  annotations: Annotation[];
  selectedTool: AnnotationType | null;
  onToolSelect: (tool: AnnotationType | null) => void;
  onAnnotationAdd: (annotation: Omit<Annotation, "id" | "timestamp">) => void;
  onAnnotationUpdate: (id: string, updates: Partial<Annotation>) => void;
  onAnnotationDelete: (id: string) => void;

  // Forms
  formFields: FormField[];
  formData: FormData;
  onFormFieldsChange: (fields: FormField[]) => void;
  onFormDataChange: (data: FormData) => void;
  onFormSubmit?: (data: FormData, validation: FormValidationResult) => void;
  onFieldAdd?: (field: FormField) => void;
  validationRules?: Record<string, unknown[]>;
  onValidationChange?: (isValid: boolean, errors: Record<string, string[]>) => void;

  // Version Control
  currentVersion?: string;
  onVersionChange?: (versionId: string) => void;
  onVersionRestore?: (versionId: string) => void;
  
  // Virtualization
  virtualizeList?: boolean;
  virtualScrollOptions?: {
    overscan?: number;
    itemSize?: number;
  };
}

export default function PDFSidebar({
  isOpen,
  onClose,
  activeTab,
  onTabChange,
  // Adaptive features
  currentMode = 'reading',
  onModeChange,
  adaptiveLayout = false,
  performanceMode = false,
  // PDF data
  pdfDocument,
  numPages,
  currentPage,
  outline,
  currentScale,
  searchText,
  onSearchChange,
  onPageSelect,
  bookmarks,
  onAddBookmark,
  onRemoveBookmark,
  onUpdateBookmark,
  annotations,
  selectedTool,
  onToolSelect,
  // onAnnotationAdd and onAnnotationUpdate are not used in this component
  // but are required by the interface for compatibility
  onAnnotationDelete,
  formFields,
  formData,
  onFormFieldsChange,
  onFormDataChange,
  onFormSubmit,
  onFieldAdd,
  currentVersion = "v1.0.0",
  onVersionChange = () => {},
  onVersionRestore = () => {},
}: PDFSidebarProps) {
  const [searchResults, setSearchResults] = useState<
    Array<{ pageIndex: number; textItems: unknown[] }>
  >([]);
  const [currentSearchIndex, setCurrentSearchIndex] = useState(-1);
  const [validationResult, setValidationResult] = useState<unknown>(null);
  const [expandedCategories, setExpandedCategories] = useState<Set<string>>(
    new Set(['Navigation', 'Content'])
  );

  // Use state variables to prevent unused variable warnings
  // These are used by child components via setters
  const debugInfo = {
    hasSearchResults: searchResults.length > 0,
    hasValidationResult: validationResult !== null,
    isSearching: currentSearchIndex >= 0
  };

  // Prevent unused variable warning
  if (process.env.NODE_ENV === 'development') {
    console.debug('PDF Sidebar Debug Info:', debugInfo);
  }

  // Viewer mode configuration
  const modeConfig: Record<ViewerMode, {
    label: string;
    icon: React.ComponentType<{ className?: string }>;
    color: string;
    description: string;
  }> = useMemo(() => ({
    reading: {
      label: 'Reading',
      icon: FileText,
      color: 'bg-blue-500',
      description: 'Browse and navigate document'
    },
    annotating: {
      label: 'Annotating',
      icon: Edit3,
      color: 'bg-green-500', 
      description: 'Add notes and highlights'
    },
    'form-filling': {
      label: 'Forms',
      icon: Layers,
      color: 'bg-purple-500',
      description: 'Fill and edit forms'
    },
    reviewing: {
      label: 'Reviewing',
      icon: Users,
      color: 'bg-orange-500',
      description: 'Review and collaborate'
    }
  }), []);

  // Toggle category expansion
  const toggleCategory = useCallback((category: string) => {
    setExpandedCategories(prev => {
      const newSet = new Set(prev);
      if (newSet.has(category)) {
        newSet.delete(category);
      } else {
        newSet.add(category);
      }
      return newSet;
    });
  }, []);

  // Define annotation tools
  const annotationTools = [
    { type: "highlight" as const, label: "Highlight", icon: PenTool },
    { type: "rectangle" as const, label: "Rectangle", icon: PenTool },
    { type: "circle" as const, label: "Circle", icon: PenTool },
    { type: "line" as const, label: "Line", icon: PenTool },
    { type: "note" as const, label: "Note", icon: PenTool },
    { type: "text" as const, label: "Text", icon: PenTool },
    { type: "freehand" as const, label: "Freehand", icon: PenTool },
  ];

  // Adaptive tab configuration with mode-based filtering
  const allTabsConfig: SidebarTabConfig[] = useMemo(() => [
    // Navigation tabs
    { id: "outline", label: "Outline", icon: List, badge: outline.length > 0 ? outline.length : null, modes: ['reading', 'annotating', 'reviewing'], category: "Navigation" },
    { id: "search", label: "Search", icon: Search, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Navigation" },
    { id: "bookmarks", label: "Bookmarks", icon: Bookmark, badge: bookmarks.length > 0 ? bookmarks.length : null, modes: ['reading', 'annotating', 'reviewing'], category: "Navigation" },
    { id: "thumbnails", label: "Thumbnails", icon: Grid3X3, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Navigation" },
    
    // Content tabs
    { id: "annotations", label: "Annotate", icon: PenTool, badge: annotations.length > 0 ? annotations.length : null, modes: ['annotating', 'reviewing'], category: "Content" },
    { id: "forms", label: "Forms", icon: FileText, badge: formFields.length > 0 ? formFields.length : null, modes: ['form-filling', 'reviewing'], category: "Content" },
    { id: "images", label: "Images", icon: ImageIcon, badge: null, modes: ['reading', 'annotating', 'reviewing'], category: "Content" },
    
    // Advanced tabs
    { id: "form-designer", label: "Designer", icon: Edit, badge: null, modes: ['form-filling'], category: "Advanced" },
    { id: "form-validation", label: "Validation", icon: CheckCircle, badge: null, modes: ['form-filling', 'reviewing'], category: "Advanced" },
    { id: "workflows", label: "Workflows", icon: GitBranch, badge: null, modes: ['reviewing'], category: "Advanced" },
    { id: "version-control", label: "Versions", icon: History, badge: null, modes: ['reviewing'], category: "Advanced" },
    { id: "version-timeline", label: "Timeline", icon: Clock, badge: null, modes: ['reviewing'], category: "Advanced" },
    
    // Tools tabs
    { id: "ocr", label: "OCR", icon: Eye, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Tools" },
    { id: "signatures", label: "Signatures", icon: Shield, badge: null, modes: ['form-filling', 'reviewing'], category: "Tools" },
    { id: "collaboration", label: "Collaborate", icon: Users, badge: null, modes: ['reviewing'], category: "Tools" },
    { id: "accessibility", label: "Accessibility", icon: Accessibility, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Tools" },
    
    // Export & Settings tabs
    { id: "export", label: "Export", icon: Download, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Export & Settings" },
    { id: "print", label: "Print", icon: Printer, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Export & Settings" },
    { id: "performance", label: "Performance", icon: Activity, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Export & Settings" },
    { id: "settings", label: "Settings", icon: Settings, badge: null, modes: ['reading', 'annotating', 'form-filling', 'reviewing'], category: "Export & Settings" },
  ], [outline.length, bookmarks.length, annotations.length, formFields.length]);

  // Filter tabs based on current mode when adaptive layout is enabled
  const availableTabs = useMemo(() => {
    if (!adaptiveLayout) return allTabsConfig;
    return allTabsConfig.filter(tab => tab.modes.includes(currentMode));
  }, [allTabsConfig, adaptiveLayout, currentMode]);

  // Group tabs by category
  const tabCategories = useMemo(() => {
    const categories: Record<string, typeof availableTabs> = {};
    availableTabs.forEach(tab => {
      if (!categories[tab.category]) {
        categories[tab.category] = [];
      }
      categories[tab.category].push(tab);
    });
    
    return Object.entries(categories).map(([name, tabs]) => ({
      name,
      tabs: tabs.map(tab => ({
        id: tab.id,
        label: tab.label,
        icon: tab.icon,
        badge: tab.badge
      }))
    }));
  }, [availableTabs]);

  // Render mode selector for adaptive layout
  const renderModeSelector = useCallback(() => {
    if (!adaptiveLayout || !onModeChange) return null;

    return (
      <div className="p-4 border-b">
        <div className="text-sm font-medium text-muted-foreground mb-3">
          Current Mode
        </div>
        <div className="grid grid-cols-2 gap-2">
          {Object.entries(modeConfig).map(([mode, config]) => (
            <Button
              key={mode}
              variant={currentMode === mode ? "default" : "ghost"}
              size="sm"
              onClick={() => onModeChange(mode as ViewerMode)}
              className={cn(
                "h-auto p-3 flex flex-col items-start gap-1 relative",
                currentMode === mode && "ring-2 ring-ring ring-offset-2"
              )}
            >
              <div className="flex items-center gap-2 w-full">
                <div className={cn("w-2 h-2 rounded-full", config.color)} />
                <config.icon className="size-4" />
                <span className="text-xs font-medium">{config.label}</span>
              </div>
              {currentMode === mode && (
                <div className="text-xs text-muted-foreground text-left">
                  {config.description}
                </div>
              )}
            </Button>
          ))}
        </div>
      </div>
    );
  }, [adaptiveLayout, onModeChange, currentMode, modeConfig]);

  // Render collapsible category section
  const renderCategorySection = useCallback((category: { name: string; tabs: Array<{ id: SidebarTab; label: string; icon: React.ComponentType<{ className?: string }>; badge?: string | number | null }> }) => {
    if (!adaptiveLayout) {
      // Original layout - always expanded
      return (
        <div key={category.name} className="space-y-1">
          <div className="text-xs font-medium text-muted-foreground px-2 py-1">
            {category.name.toUpperCase()}
          </div>
          <div className="grid grid-cols-2 gap-1">
            {category.tabs.map((tab) => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? "default" : "ghost"}
                size="sm"
                onClick={() => onTabChange(tab.id)}
                className="flex items-center gap-1 justify-start h-8 text-xs"
              >
                <tab.icon className="h-3 w-3 flex-shrink-0" />
                <span className="truncate flex-1 text-left">
                  {tab.label}
                </span>
                {tab.badge && (
                  <Badge
                    variant="secondary"
                    className="text-xs h-4 px-1 flex-shrink-0"
                  >
                    {tab.badge}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        </div>
      );
    }

    // Adaptive layout - collapsible categories
    const isExpanded = expandedCategories.has(category.name);
    
    return (
      <div key={category.name} className="mb-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => toggleCategory(category.name)}
          className="w-full justify-between p-2 h-auto font-medium text-muted-foreground hover:text-foreground"
        >
          <span className="text-xs uppercase tracking-wide">
            {category.name}
          </span>
          {isExpanded ? (
            <ChevronDown className="size-3" />
          ) : (
            <ChevronRight className="size-3" />
          )}
        </Button>
        
        {isExpanded && (
          <div className="space-y-1 pl-2">
            {category.tabs.map(tab => (
              <Button
                key={tab.id}
                variant={activeTab === tab.id ? "secondary" : "ghost"}
                size="sm"
                onClick={() => onTabChange(tab.id)}
                className="w-full justify-start gap-3 px-3 py-2 h-auto"
              >
                <tab.icon className="size-4 shrink-0" />
                <span className="flex-1 text-left truncate">{tab.label}</span>
                {tab.badge && (
                  <Badge variant="secondary" className="ml-auto text-xs">
                    {tab.badge}
                  </Badge>
                )}
              </Button>
            ))}
          </div>
        )}
      </div>
    );
  }, [adaptiveLayout, expandedCategories, toggleCategory, activeTab, onTabChange]);



  const renderTabContent = () => {
    switch (activeTab) {
      case "outline":
        return (
          <PDFOutline
            outline={outline}
          />
        );

      case "search":
        return (
          <PDFSearch
            searchText={searchText}
            onSearchChange={onSearchChange}
            onClose={() => {}}
            pdfDocument={pdfDocument}
            numPages={numPages}
            onPageSelect={onPageSelect}
            onSearchResults={setSearchResults}
            onCurrentSearchIndex={setCurrentSearchIndex}
          />
        );

      case "bookmarks":
        return (
          <PDFBookmarks
            bookmarks={bookmarks}
            currentPage={currentPage}
            onPageSelect={onPageSelect}
            onAddBookmark={onAddBookmark}
            onRemoveBookmark={onRemoveBookmark}
            onUpdateBookmark={onUpdateBookmark}
          />
        );

      case "annotations":
        return (
          <PDFAnnotations
            annotations={annotations}
            selectedTool={selectedTool}
            onToolSelect={(tool: string | null) => onToolSelect(tool as AnnotationType | null)}
            onAnnotationDelete={onAnnotationDelete}
            annotationTools={annotationTools}
          />
        );
      case "forms":
        return (
          <PDFFormManager
            pdfDocument={pdfDocument}
            numPages={numPages}
            currentPage={currentPage}
            onFormFieldsChange={onFormFieldsChange}
            onFormDataChange={onFormDataChange}
            onFormSubmit={onFormSubmit}
            onFieldAdd={onFieldAdd}
          />
        );

      case "form-designer":
        return (
          <PDFFormDesigner
            formFields={formFields}
            currentPage={currentPage}
            onFormFieldsChange={onFormFieldsChange}
          />
        );

      case "form-validation":
        return (
          <PDFFormValidation
            formFields={formFields}
            formData={formData}
            onValidationChange={setValidationResult}
          />
        );

      case "workflows":
        return (
          <PDFWorkflowManager
            formFields={formFields}
            formData={formData}
            currentUser="current-user"
          />
        );

      case "version-control":
        return (
          <PDFVersionControl
            pdfDocument={pdfDocument}
            currentVersion={currentVersion}
            onVersionChange={onVersionChange}
            onVersionRestore={onVersionRestore}
          />
        );

      case "version-timeline":
        return (
          <PDFVersionTimeline
            versions={[]}
            branches={[]}
            currentVersion={currentVersion}
            onVersionSelect={onVersionChange}
            onVersionRestore={onVersionRestore}
          />
        );

      case "ocr":
        return <PDFOCREngine pdfDocument={pdfDocument} numPages={numPages} />;

      case "signatures":
        return (
          <PDFDigitalSignature
            pdfDocument={pdfDocument}
            numPages={numPages}
            currentPage={currentPage}
          />
        );

      case "collaboration":
        return (
          <PDFCollaboration
            pdfDocument={pdfDocument}
            currentUser={{
              id: "current-user",
              name: "Current User",
              email: "<EMAIL>",
              role: "owner",
              status: "online",
              lastSeen: new Date(),
              permissions: {
                canView: true,
                canEdit: true,
                canComment: true,
                canShare: true,
                canDownload: true,
                canDelete: true,
                canManageUsers: false,
                canExport: true,
                canPrint: true,
              },
            }}
          />
        );

      case "accessibility":
        return (
          <PDFAccessibility
            pdfDocument={pdfDocument}
            numPages={numPages}
            currentPage={currentPage}
          />
        );

      case "thumbnails":
        return (
          <PDFThumbnailView
            pdfDocument={pdfDocument}
            numPages={numPages}
            currentPage={currentPage}
            onPageSelect={onPageSelect}
            annotations={annotations}
            bookmarks={bookmarks}
          />
        );

      case "images":
        return (
          <PDFImageExtractor pdfDocument={pdfDocument} numPages={numPages} />
        );

      case "export":
        return (
          <PDFAnnotationExport
            annotations={annotations}
            numPages={numPages}
            documentTitle="PDF Document"
          />
        );

      case "print":
        return (
          <PDFPrintManager
            pdfDocument={pdfDocument}
            numPages={numPages}
            annotations={annotations}
            currentScale={currentScale}
          />
        );

      case "performance":
        return (
          <PDFPerformanceMonitor />
        );
      case "settings":
        return (
          <div className="p-4 space-y-4">
            <h3 className="font-medium">PDF Settings</h3>
            <div className="space-y-2 text-sm text-muted-foreground">
              <p>• Text selection enabled</p>
              <p>• Annotations visible</p>
              <p>• Form fields interactive</p>
              <p>• High quality rendering</p>
              <p>• Performance monitoring active</p>
              <p>• Auto-save annotations & forms</p>
              <p>• OCR text recognition</p>
              <p>• Digital signature support</p>
              <p>• Real-time collaboration</p>
              <p>• Accessibility features</p>
              <p>• Version control enabled</p>
              <p>• Document timeline tracking</p>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <>
      {/* Backdrop */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <div
        data-testid="pdf-sidebar"
        role="complementary"
        aria-label="PDF Navigation Sidebar"
        className={cn(
          "fixed top-0 left-0 h-full bg-background border-r shadow-lg z-50 transition-transform duration-300 ease-in-out flex flex-col",
          "w-80 lg:w-96",
          isOpen ? "translate-x-0" : "-translate-x-full",
          adaptiveLayout && "adaptive-sidebar",
          performanceMode && "performance-sidebar"
        )}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b">
          <div className="flex items-center gap-2">
            <h2 className="font-semibold text-lg">PDF Tools</h2>
            {adaptiveLayout && (
              <Badge variant="outline" className="text-xs">
                {modeConfig[currentMode].label}
              </Badge>
            )}
          </div>
          <Button variant="ghost" size="sm" onClick={onClose}>
            <X className="h-4 w-4" />
          </Button>
        </div>

        {/* Mode Selector - only shown in adaptive layout */}
        {renderModeSelector()}

        {/* Tab Navigation */}
        <div className="border-b max-h-60 min-h-32">
          <ScrollArea className="h-full">
            <div className="p-2 space-y-3">
              {tabCategories.map((category) => renderCategorySection(category))}
            </div>
          </ScrollArea>
        </div>

        {/* Content */}
        <div className="flex-1 min-h-0 overflow-hidden">
          <ScrollArea className="h-full">
            <div className="p-1">{renderTabContent()}</div>
          </ScrollArea>
        </div>

        {/* Footer */}
        <div className="border-t p-3 bg-muted/30">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>
              Page {currentPage} of {numPages}
            </span>
            <Button
              variant="ghost"
              size="sm"
              onClick={onClose}
              className="lg:hidden"
            >
              <ChevronLeft className="h-4 w-4 mr-1" />
              Close
            </Button>
          </div>
        </div>
      </div>
    </>
  );
}
