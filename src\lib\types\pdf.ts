import type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Re-export the proper PDF types from pdfjs-dist
export type { PDFDocumentProxy, PDFPageProxy } from 'pdfjs-dist';

// Type for the document callback from react-pdf
export type DocumentCallback = PDFDocumentProxy;

// Type for page callback from react-pdf
export type PageCallback = PDFPageProxy & {
  width: number;
  height: number;
  originalWidth: number;
  originalHeight: number;
};

// Union type for PDF document that can be either the direct proxy or wrapped
export type PDFDocument = PDFDocumentProxy | {
  _pdfInfo?: {
    pdfDocument: PDFDocumentProxy;
  };
} | unknown;

// Type guard to check if an object is a PDFDocumentProxy
export function isPDFDocumentProxy(obj: unknown): obj is PDFDocumentProxy {
  return (
    obj !== null &&
    typeof obj === 'object' &&
    'numPages' in obj &&
    'getPage' in obj &&
    typeof (obj as Record<string, unknown>).getPage === 'function'
  );
}

// Helper function to extract the actual PDF document from various formats
export function extractPDFDocument(pdfDocument: PDFDocument): PDFDocumentProxy | null {
  if (!pdfDocument) return null;
  
  // Check if it's already a PDFDocumentProxy
  if (isPDFDocumentProxy(pdfDocument)) {
    return pdfDocument;
  }
  
  // Check if it has the _pdfInfo wrapper (legacy format)
  if (
    typeof pdfDocument === 'object' &&
    pdfDocument !== null &&
    '_pdfInfo' in pdfDocument &&
    pdfDocument._pdfInfo &&
    typeof pdfDocument._pdfInfo === 'object' &&
    'pdfDocument' in pdfDocument._pdfInfo
  ) {
    const wrapped = pdfDocument._pdfInfo.pdfDocument;
    if (isPDFDocumentProxy(wrapped)) {
      return wrapped;
    }
  }
  
  return null;
}

// Type for outline items
export interface OutlineItem {
  title: string;
  bold?: boolean;
  italic?: boolean;
  color?: [number, number, number] | null;
  dest?: unknown;
  url?: string;
  items?: OutlineItem[];
}

// Type for text content items
export interface TextContentItem {
  str: string;
  dir: string;
  width: number;
  height: number;
  transform: number[];
  fontName: string;
  hasEOL: boolean;
}

// Type for text content
export interface TextContent {
  items: TextContentItem[];
  styles: Record<string, unknown>;
}
