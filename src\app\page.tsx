"use client"

import { useState } from "react"
import { PDFUpload, PDFViewer } from "@/components"

export default function Home() {
  const [selectedFile, setSelectedFile] = useState<string | File | null>(null)

  const handleFileSelect = (file: string | File) => {
    setSelectedFile(file)
  }

  const handleClose = () => {
    setSelectedFile(null)
  }

  if (selectedFile) {
    return <PDFViewer file={selectedFile} onClose={handleClose} />
  }

  return <PDFUpload onFileSelect={handleFileSelect} />
}
